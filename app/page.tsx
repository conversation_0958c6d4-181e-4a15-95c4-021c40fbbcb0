"use client"

import { useState, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  MessageCircle,
  Users,
  TrendingUp,
  Activity,
  User,
  Calendar,
  Filter,
  ChevronDown,
  ChevronUp,
} from "lucide-react"
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
} from "recharts"

// Import data from external file
import data from "../data.json"

// Extended mock data for comprehensive filtering
const generateExtendedData = () => {
  const data = []
  const startDate = new Date("2024-01-01")
  const endDate = new Date("2025-12-31")

  for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
    const dayOfWeek = d.getDay()
    const month = d.getMonth()
    const year = d.getFullYear()
    const hour = Math.floor(Math.random() * 24)

    // Simulate realistic activity patterns with yearly variations
    let baseActivity = 20

    // Weekday patterns (Monday-Friday more active)
    if (dayOfWeek >= 1 && dayOfWeek <= 5) baseActivity += 30

    // Seasonal patterns (Fall months more active)
    if (month >= 8 && month <= 11) baseActivity += 20

    // Business hours more active (9 AM - 5 PM)
    if (hour >= 9 && hour <= 17) baseActivity += 25

    // Year-over-year growth simulation (2025 slightly more active)
    if (year === 2025) baseActivity += 10

    // Holiday periods (December/January slightly less active)
    if (month === 11 || month === 0) baseActivity -= 5

    // Summer vacation period (July/August slightly less active)
    if (month === 6 || month === 7) baseActivity -= 8

    // Add some random variation and ensure minimum activity
    const activity = Math.max(5, Math.floor(baseActivity + Math.random() * 40))
    const messages = Math.max(3, Math.floor(activity * (0.8 + Math.random() * 0.4)))

    data.push({
      date: new Date(d),
      dateString: d.toISOString().split("T")[0],
      dayOfWeek: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"][dayOfWeek],
      month: d.toLocaleString("default", { month: "long" }),
      year: d.getFullYear(),
      hour,
      activity,
      messages,
    })
  }

  return data
}

const extendedActivityData = generateExtendedData()

// Extract data from imported JSON
const { mostFrequentEmojis, topContributors, sentimentData, memberActivity, recentMessages } = data

// Enhanced Avatar Component with Iranian Avatar Service
const ProfileAvatar = ({
  src,
  alt,
  initials,
  hasImage = false,
  size = "default",
  className = "",
}: {
  src?: string | null
  alt: string
  initials: string
  hasImage?: boolean
  size?: "small" | "default" | "large"
  className?: string
}) => {
  const sizeClasses = {
    small: "h-6 w-6",
    default: "h-8 w-8",
    large: "h-12 w-12",
  }

  const iconSizes = {
    small: "h-3 w-3",
    default: "h-4 w-4",
    large: "h-6 w-6",
  }

  return (
    <Avatar className={`${sizeClasses[size]} ${className} ring-2 ring-white shadow-sm`}>
      {hasImage && src ? (
        <AvatarImage
          src={src || "/placeholder.svg"}
          alt={alt}
          className="object-cover"
          crossOrigin="anonymous"
          onError={(e) => {
            console.log("Avatar failed to load:", src)
            e.currentTarget.style.display = "none"
          }}
        />
      ) : null}
      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-medium text-xs">
        {hasImage && src ? (
          initials
        ) : (
          <div className="flex items-center justify-center">
            <User className={`${iconSizes[size]} text-white`} />
          </div>
        )}
      </AvatarFallback>
    </Avatar>
  )
}

// Custom tooltip components
const CustomTooltip = ({ active, payload, label, labelFormatter, valueFormatter }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-lg border bg-white p-3 shadow-lg">
        <div className="mb-2">
          <span className="text-sm font-medium text-gray-900">{labelFormatter ? labelFormatter(label) : label}</span>
        </div>
        <div className="space-y-1">
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full" style={{ backgroundColor: entry.color }} />
                <span className="text-sm text-gray-600">{entry.name || entry.dataKey}</span>
              </div>
              <span className="text-sm font-semibold text-gray-900">
                {valueFormatter ? valueFormatter(entry.value) : entry.value}
              </span>
            </div>
          ))}
        </div>
      </div>
    )
  }
  return null
}

const PieTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload
    return (
      <div className="rounded-lg border bg-white p-3 shadow-lg">
        <div className="flex items-center space-x-2 mb-1">
          <div className="w-3 h-3 rounded-full" style={{ backgroundColor: data.color }} />
          <span className="text-sm font-medium text-gray-900">{data.name}</span>
        </div>
        <div className="text-sm text-gray-600">
          <div>Percentage: {data.value}%</div>
          <div>Messages: {data.count}</div>
        </div>
      </div>
    )
  }
  return null
}

export default function WhatsAppAnalyticsDashboard() {
  const getActivityBadgeColor = (status: string) => {
    switch (status) {
      case "Very Active":
        return "bg-green-100 text-green-800 hover:bg-green-100"
      case "Active":
        return "bg-blue-100 text-blue-800 hover:bg-blue-100"
      case "Moderate":
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
      case "Low":
        return "bg-gray-100 text-gray-800 hover:bg-gray-100"
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100"
    }
  }

  // Pagination state and logic
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(5)

  // Date filtering state
  const [startDate, setStartDate] = useState("2024-01-01")
  const [endDate, setEndDate] = useState("2025-05-28") // Changed from 2025-12-31 to current date
  const [timelineView, setTimelineView] = useState<"daily" | "weekly" | "monthly" | "yearly">("monthly")

  // Filter collapse state
  const [isFilterExpanded, setIsFilterExpanded] = useState(false)

  // Zoom and scroll state for daily timeline
  const [zoomLevel, setZoomLevel] = useState(1)
  const [scrollPosition, setScrollPosition] = useState(0)

  // Filter data based on date range
  const filteredData = useMemo(() => {
    return extendedActivityData.filter((item) => {
      const itemDate = item.dateString
      return itemDate >= startDate && itemDate <= endDate
    })
  }, [startDate, endDate])

  // Process data for different timeline views
  const processedTimelineData = useMemo(() => {
    const grouped: { [key: string]: { activity: number; messages: number; count: number } } = {}

    filteredData.forEach((item) => {
      let key = ""

      switch (timelineView) {
        case "daily":
          key = item.dateString
          break
        case "weekly":
          const weekStart = new Date(item.date)
          weekStart.setDate(weekStart.getDate() - weekStart.getDay())
          key = weekStart.toISOString().split("T")[0]
          break
        case "monthly":
          key = `${item.year}-${String(item.date.getMonth() + 1).padStart(2, "0")}`
          break
        case "yearly":
          key = String(item.year)
          break
      }

      if (!grouped[key]) {
        grouped[key] = { activity: 0, messages: 0, count: 0 }
      }

      grouped[key].activity += item.activity
      grouped[key].messages += item.messages
      grouped[key].count += 1
    })

    return Object.entries(grouped)
      .map(([key, data]) => ({
        period: key,
        activity: Math.round(data.activity / data.count),
        messages: Math.round(data.messages / data.count),
        totalActivity: data.activity,
        totalMessages: data.messages,
      }))
      .sort((a, b) => a.period.localeCompare(b.period))
  }, [filteredData, timelineView])

  // Process data for day of week analysis
  const dayOfWeekData = useMemo(() => {
    const grouped: { [key: string]: { activity: number; count: number } } = {}

    filteredData.forEach((item) => {
      const day = item.dayOfWeek
      if (!grouped[day]) {
        grouped[day] = { activity: 0, count: 0 }
      }
      grouped[day].activity += item.activity
      grouped[day].count += 1
    })

    const daysOrder = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
    return daysOrder.map((day) => ({
      day,
      frequency: grouped[day] ? Math.round(grouped[day].activity / grouped[day].count) : 0,
    }))
  }, [filteredData])

  // Process data for month analysis
  const monthData = useMemo(() => {
    const grouped: { [key: string]: { activity: number; count: number } } = {}

    filteredData.forEach((item) => {
      const month = item.month
      if (!grouped[month]) {
        grouped[month] = { activity: 0, count: 0 }
      }
      grouped[month].activity += item.activity
      grouped[month].count += 1
    })

    const monthsOrder = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ]
    return monthsOrder.map((month) => ({
      month: month.slice(0, 3),
      frequency: grouped[month] ? Math.round(grouped[month].activity / grouped[month].count) : 0,
    }))
  }, [filteredData])

  // Process data for hourly message volume
  const hourlyData = useMemo(() => {
    const grouped: { [key: number]: { messages: number; count: number } } = {}

    filteredData.forEach((item) => {
      const hour = item.hour
      if (!grouped[hour]) {
        grouped[hour] = { messages: 0, count: 0 }
      }
      grouped[hour].messages += item.messages
      grouped[hour].count += 1
    })

    return Array.from({ length: 24 }, (_, hour) => ({
      time: `${String(hour).padStart(2, "0")}:00`,
      hour: hour === 0 ? "12 AM" : hour === 12 ? "12 PM" : hour < 12 ? `${hour} AM` : `${hour - 12} PM`,
      messages: grouped[hour] ? Math.round(grouped[hour].messages / grouped[hour].count) : 0,
    }))
  }, [filteredData])

  // Calculate pagination
  const totalPages = Math.ceil(memberActivity.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentMembers = memberActivity.slice(startIndex, endIndex)

  // Pagination handlers
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)))
  }

  const goToPrevious = () => {
    setCurrentPage((prev) => Math.max(1, prev - 1))
  }

  const goToNext = () => {
    setCurrentPage((prev) => Math.min(totalPages, prev + 1))
  }

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage)
    setCurrentPage(1)
  }

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = []
    const maxVisiblePages = 5

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      const startPage = Math.max(1, currentPage - 2)
      const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i)
      }
    }

    return pages
  }

  // Quick date range presets
  const setQuickDateRange = (range: string) => {
    const today = new Date("2025-05-28") // Use current date as "today"
    let newStartDate = new Date()
    let newEndDate = new Date(today) // Create a copy for end date

    switch (range) {
      case "last7days":
        // Last 7 days: go back 7 days from today
        newStartDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
        newEndDate = new Date(today)
        break
      case "last30days":
        // Last 30 days: go back 30 days from today
        newStartDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
        newEndDate = new Date(today)
        break
      case "last3months":
        // Last 3 months: go back 3 complete months
        newEndDate = new Date(today.getFullYear(), today.getMonth() + 1, 0) // Last day of current month (May 31)
        newStartDate = new Date(today.getFullYear(), today.getMonth() - 2, 1) // First day of 3 months ago (March 1)
        break
      case "last6months":
        // Last 6 months: go back 6 complete months
        newEndDate = new Date(today.getFullYear(), today.getMonth() + 1, 0) // Last day of current month (May 31)
        newStartDate = new Date(today.getFullYear(), today.getMonth() - 5, 1) // First day of 6 months ago (December 1, 2024)
        break
      case "2024":
        newStartDate = new Date("2024-01-01")
        newEndDate = new Date("2024-12-31")
        break
      case "2025":
        newStartDate = new Date("2025-01-01")
        newEndDate = new Date("2025-12-31")
        break
      case "q1-2024":
        newStartDate = new Date("2024-01-01")
        newEndDate = new Date("2024-03-31")
        break
      case "q2-2024":
        newStartDate = new Date("2024-04-01")
        newEndDate = new Date("2024-06-30")
        break
      case "q3-2024":
        newStartDate = new Date("2024-07-01")
        newEndDate = new Date("2024-09-30")
        break
      case "q4-2024":
        newStartDate = new Date("2024-10-01")
        newEndDate = new Date("2024-12-31")
        break
      case "q1-2025":
        newStartDate = new Date("2025-01-01")
        newEndDate = new Date("2025-03-31")
        break
      case "q2-2025":
        newStartDate = new Date("2025-04-01")
        newEndDate = new Date("2025-06-30")
        break
      case "q3-2025":
        newStartDate = new Date("2025-07-01")
        newEndDate = new Date("2025-09-30")
        break
      case "q4-2025":
        newStartDate = new Date("2025-10-01")
        newEndDate = new Date("2025-12-31")
        break
      case "alltime":
        newStartDate = new Date("2024-01-01")
        newEndDate = new Date("2025-12-31")
        break
      default:
        return
    }

    setStartDate(newStartDate.toISOString().split("T")[0])
    setEndDate(newEndDate.toISOString().split("T")[0])
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900">WhatsApp Group Analytics</h1>
          <p className="text-gray-600 text-lg">Team Collaboration Hub - Insights & Metrics</p>
        </div>

        {/* Key Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Members</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">160</div>
              <p className="text-xs text-muted-foreground">+12 from last month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Daily Messages</CardTitle>
              <MessageCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {filteredData.length > 0
                  ? Math.round(filteredData.reduce((sum, item) => sum + item.messages, 0) / filteredData.length)
                  : 0}
              </div>
              <p className="text-xs text-muted-foreground">Average in selected period</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Days</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{filteredData.length}</div>
              <p className="text-xs text-muted-foreground">Days in selected range</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Peak Activity</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {filteredData.length > 0 ? Math.max(...filteredData.map((item) => item.activity)) : 0}
              </div>
              <p className="text-xs text-muted-foreground">Highest single day</p>
            </CardContent>
          </Card>
        </div>

        {/* Collapsible Date and Timeline Filters */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Filter className="h-5 w-5" />
                <div>
                  <CardTitle>Data Filters & Timeline Views</CardTitle>
                  <CardDescription>
                    {isFilterExpanded
                      ? "Customize your data analysis with date ranges and timeline perspectives"
                      : `Current: ${startDate} to ${endDate} • ${timelineView.charAt(0).toUpperCase() + timelineView.slice(1)} view`}
                  </CardDescription>
                </div>
              </div>
              <button
                onClick={() => setIsFilterExpanded(!isFilterExpanded)}
                className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                aria-label={isFilterExpanded ? "Collapse filters" : "Expand filters"}
              >
                <span>{isFilterExpanded ? "Collapse" : "Expand"}</span>
                {isFilterExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </button>
            </div>
          </CardHeader>

          {isFilterExpanded && (
            <CardContent className="transition-all duration-200 ease-in-out">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Date Range Picker */}
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-gray-900">Date Range</h3>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Start Date</label>
                      <input
                        type="date"
                        value={startDate}
                        onChange={(e) => setStartDate(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">End Date</label>
                      <input
                        type="date"
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>

                {/* Quick Date Presets */}
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-gray-900">Quick Presets</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {[
                      { label: "Last 7 Days", value: "last7days" },
                      { label: "Last 30 Days", value: "last30days" },
                      { label: "Last 3 Months", value: "last3months" },
                      { label: "Last 6 Months", value: "last6months" },
                      { label: "All Time", value: "alltime" },
                      { label: "Year 2024", value: "2024" },
                      { label: "Year 2025", value: "2025" },
                      { label: "Q1 2024", value: "q1-2024" },
                      { label: "Q2 2024", value: "q2-2024" },
                      { label: "Q3 2024", value: "q3-2024" },
                      { label: "Q4 2024", value: "q4-2024" },
                      { label: "Q1 2025", value: "q1-2025" },
                      { label: "Q2 2025", value: "q2-2025" },
                      { label: "Q3 2025", value: "q3-2025" },
                      { label: "Q4 2025", value: "q4-2025" },
                    ].map((preset) => (
                      <button
                        key={preset.value}
                        onClick={() => setQuickDateRange(preset.value)}
                        className="px-3 py-2 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                      >
                        {preset.label}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Timeline View Selector */}
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-gray-900">Timeline View</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {[
                      { label: "Daily", value: "daily" as const },
                      { label: "Weekly", value: "weekly" as const },
                      { label: "Monthly", value: "monthly" as const },
                      { label: "Yearly", value: "yearly" as const },
                    ].map((view) => (
                      <button
                        key={view.value}
                        onClick={() => setTimelineView(view.value)}
                        className={`px-3 py-2 text-xs font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                          timelineView === view.value
                            ? "bg-blue-600 text-white"
                            : "text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
                        }`}
                      >
                        {view.label}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          )}
        </Card>

        {/* Dynamic Timeline Chart with Zoom and Scroll */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Calendar className="h-5 w-5" />
                <div>
                  <CardTitle>{timelineView.charAt(0).toUpperCase() + timelineView.slice(1)} Timeline</CardTitle>
                  <CardDescription>
                    Activity patterns over {timelineView} periods from {startDate} to {endDate}
                  </CardDescription>
                </div>
              </div>

              {/* Zoom and Scroll Controls - Only show for daily view */}
              {timelineView === "daily" && processedTimelineData.length > 30 && (
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                    <button
                      onClick={() => setZoomLevel(Math.max(0.5, zoomLevel - 0.5))}
                      disabled={zoomLevel <= 0.5}
                      className="p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Zoom Out"
                    >
                      <span className="text-lg">−</span>
                    </button>
                    <span className="px-3 py-1 text-sm font-medium text-gray-700 min-w-[60px] text-center">
                      {Math.round(zoomLevel * 100)}%
                    </span>
                    <button
                      onClick={() => setZoomLevel(Math.min(5, zoomLevel + 0.5))}
                      disabled={zoomLevel >= 5}
                      className="p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Zoom In"
                    >
                      <span className="text-lg">+</span>
                    </button>
                  </div>

                  {zoomLevel > 1 && (
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => setScrollPosition(Math.max(0, scrollPosition - 10))}
                        disabled={scrollPosition <= 0}
                        className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Scroll Left"
                      >
                        ←
                      </button>
                      <button
                        onClick={() =>
                          setScrollPosition(
                            Math.min(processedTimelineData.length - Math.floor(30 / zoomLevel), scrollPosition + 10),
                          )
                        }
                        disabled={scrollPosition >= processedTimelineData.length - Math.floor(30 / zoomLevel)}
                        className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Scroll Right"
                      >
                        →
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-[400px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={
                    timelineView === "daily" && processedTimelineData.length > 30
                      ? processedTimelineData.slice(scrollPosition, scrollPosition + Math.floor(30 / zoomLevel))
                      : processedTimelineData
                  }
                  margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis
                    dataKey="period"
                    tick={{ fontSize: 12 }}
                    angle={timelineView === "daily" ? -45 : -45}
                    textAnchor="end"
                    height={80}
                    interval={timelineView === "daily" && zoomLevel >= 2 ? 0 : "preserveStartEnd"}
                  />
                  <YAxis
                    tick={{ fontSize: 12 }}
                    label={{ value: "Activity Level", angle: -90, position: "insideLeft" }}
                  />
                  <Tooltip
                    content={
                      <CustomTooltip
                        labelFormatter={(label: string) => `Period: ${label}`}
                        valueFormatter={(value: number) => `${value} avg activity`}
                      />
                    }
                  />
                  <Line
                    type="monotone"
                    dataKey="activity"
                    stroke="#3b82f6"
                    strokeWidth={3}
                    dot={{ fill: "#3b82f6", strokeWidth: 2, r: zoomLevel >= 2 ? 5 : 4 }}
                    activeDot={{ r: zoomLevel >= 2 ? 7 : 6, stroke: "#3b82f6", strokeWidth: 2 }}
                    name="Activity Level"
                  />
                  <Line
                    type="monotone"
                    dataKey="messages"
                    stroke="#10b981"
                    strokeWidth={2}
                    dot={{ fill: "#10b981", strokeWidth: 1, r: zoomLevel >= 2 ? 4 : 3 }}
                    activeDot={{ r: zoomLevel >= 2 ? 6 : 5, stroke: "#10b981", strokeWidth: 2 }}
                    name="Messages"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>

            {/* Timeline Navigation Info */}
            {timelineView === "daily" && processedTimelineData.length > 30 && zoomLevel > 1 && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-between text-sm text-blue-800">
                  <span>
                    Showing {scrollPosition + 1} to{" "}
                    {Math.min(scrollPosition + Math.floor(30 / zoomLevel), processedTimelineData.length)} of{" "}
                    {processedTimelineData.length} days
                  </span>
                  <span>Zoom: {Math.round(zoomLevel * 100)}% • Use scroll buttons to navigate</span>
                </div>

                {/* Progress bar */}
                <div className="mt-2 w-full bg-blue-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-200"
                    style={{
                      width: `${Math.min(100, (Math.floor(30 / zoomLevel) / processedTimelineData.length) * 100)}%`,
                      marginLeft: `${(scrollPosition / processedTimelineData.length) * 100}%`,
                    }}
                  />
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Activity Pattern Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Most Busy Day of Week */}
          <Card>
            <CardHeader>
              <CardTitle>Most Busy Day of Week</CardTitle>
              <CardDescription>Peak activity days in selected period</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[350px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={dayOfWeekData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis
                      dataKey="day"
                      tick={{ fontSize: 12 }}
                      angle={-45}
                      textAnchor="end"
                      height={60}
                      interval={0}
                    />
                    <YAxis
                      tick={{ fontSize: 12 }}
                      label={{ value: "Avg Activity", angle: -90, position: "insideLeft" }}
                    />
                    <Tooltip
                      content={
                        <CustomTooltip
                          labelFormatter={(label: string) => `Day: ${label}`}
                          valueFormatter={(value: number) => `${value} avg activity`}
                        />
                      }
                    />
                    <Bar dataKey="frequency" fill="#10b981" radius={[4, 4, 0, 0]} name="Activity Level" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Most Busy Month */}
          <Card>
            <CardHeader>
              <CardTitle>Most Busy Month of Year</CardTitle>
              <CardDescription>Peak activity months in selected period</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[350px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={monthData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="month" tick={{ fontSize: 12 }} angle={-45} textAnchor="end" height={60} />
                    <YAxis
                      tick={{ fontSize: 12 }}
                      label={{ value: "Avg Activity", angle: -90, position: "insideLeft" }}
                    />
                    <Tooltip
                      content={
                        <CustomTooltip
                          labelFormatter={(label: string) => `Month: ${label}`}
                          valueFormatter={(value: number) => `${value} avg activity`}
                        />
                      }
                    />
                    <Bar dataKey="frequency" fill="#8b5cf6" radius={[4, 4, 0, 0]} name="Monthly Activity" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Message Volume by Hour */}
        <Card>
          <CardHeader>
            <CardTitle>Message Volume by Hour</CardTitle>
            <CardDescription>Peak messaging times in selected period</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[350px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={hourlyData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis dataKey="time" tick={{ fontSize: 12 }} angle={-45} textAnchor="end" height={60} />
                  <YAxis
                    tick={{ fontSize: 12 }}
                    label={{ value: "Avg Messages", angle: -90, position: "insideLeft" }}
                  />
                  <Tooltip
                    content={
                      <CustomTooltip
                        labelFormatter={(label: string) => `Time: ${label}`}
                        valueFormatter={(value: number) => `${value} avg messages`}
                      />
                    }
                  />
                  <Bar dataKey="messages" fill="#f59e0b" radius={[4, 4, 0, 0]} name="Message Count" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Charts Row 2 */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Top Contributors */}
          <Card>
            <CardHeader>
              <CardTitle>Top Contributors</CardTitle>
              <CardDescription>Most active members by message count</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {topContributors.map((contributor, index) => (
                <div key={contributor.name} className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2 flex-1">
                    <span className="text-sm font-medium text-gray-500 w-4">#{index + 1}</span>
                    <ProfileAvatar
                      src={contributor.avatar}
                      alt={contributor.name}
                      initials={contributor.initials}
                      hasImage={contributor.hasImage}
                      size="default"
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{contributor.name}</p>
                    </div>
                  </div>
                  <div className="text-sm font-semibold text-gray-900">{contributor.messages.toLocaleString()}</div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Most Frequent Emojis */}
          <Card>
            <CardHeader>
              <CardTitle>Most Frequent Emojis</CardTitle>
              <CardDescription>Popular emojis used in conversations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {mostFrequentEmojis.map((emoji) => (
                  <div key={emoji.rank} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-gray-500 w-4">{emoji.rank}</span>
                      <span className="text-2xl" title={emoji.name}>
                        {emoji.emoji}
                      </span>
                    </div>
                    <span className="text-sm font-semibold text-gray-900">{emoji.count}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Sentiment Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>Sentiment Analysis</CardTitle>
              <CardDescription>Overall tone of group conversations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[280px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={sentimentData}
                      cx="50%"
                      cy="50%"
                      innerRadius={50}
                      outerRadius={100}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {sentimentData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip content={<PieTooltip />} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="mt-4 space-y-3">
                {sentimentData.map((item) => (
                  <div key={item.name} className="flex justify-between items-center">
                    <div className="flex items-center space-x-3">
                      <div className="w-4 h-4 rounded-full" style={{ backgroundColor: item.color }} />
                      <span className="text-sm font-medium text-gray-900">{item.name}</span>
                      <span className="text-sm text-gray-500">({item.value}%)</span>
                    </div>
                    <div className="text-sm font-semibold text-gray-900">{item.count} messages</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Messages */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Messages</CardTitle>
              <CardDescription>Latest group activity</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {recentMessages.map((message) => (
                <div key={message.id} className="space-y-2">
                  <div className="flex items-start space-x-2">
                    <ProfileAvatar
                      src={message.avatar}
                      alt={message.sender}
                      initials={message.initials}
                      hasImage={message.hasImage}
                      size="small"
                      className="mt-0.5"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-xs font-medium text-gray-900">{message.sender}</p>
                        <p className="text-xs text-gray-500">{message.timestamp}</p>
                      </div>
                      <p className="text-sm text-gray-700 mt-1">{message.message}</p>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Member Activity Table */}
        <Card>
          <CardHeader>
            <CardTitle>Member Activity Scores</CardTitle>
            <CardDescription>Comprehensive activity metrics for all group members</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Items per page selector and info */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={5}>5</option>
                  <option value={10}>10</option>
                  <option value={15}>15</option>
                  <option value={20}>20</option>
                </select>
                <span className="text-sm text-gray-600">entries</span>
              </div>

              <div className="text-sm text-gray-600">
                Showing {startIndex + 1} to {Math.min(endIndex, memberActivity.length)} of {memberActivity.length}{" "}
                members
              </div>
            </div>

            {/* Table */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Member</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Messages</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Reactions</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Activity Score</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {currentMembers.map((member, index) => (
                    <tr key={member.name} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-3">
                          <span className="text-sm text-gray-500">#{startIndex + index + 1}</span>
                          <ProfileAvatar
                            src={member.avatar}
                            alt={member.name}
                            initials={member.initials}
                            hasImage={member.hasImage}
                            size="default"
                          />
                          <span className="font-medium text-gray-900">{member.name}</span>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-gray-700">{member.messages.toLocaleString()}</td>
                      <td className="py-3 px-4 text-gray-700">{member.reactions.toLocaleString()}</td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${member.score}%` }} />
                          </div>
                          <span className="text-sm font-medium text-gray-900">{member.score}</span>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <Badge className={getActivityBadgeColor(member.status)}>{member.status}</Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination Controls */}
            {totalPages > 1 && (
              <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-6 pt-4 border-t">
                {/* Page info */}
                <div className="text-sm text-gray-600">
                  Page {currentPage} of {totalPages}
                </div>

                {/* Pagination buttons */}
                <div className="flex items-center space-x-2">
                  {/* Previous button */}
                  <button
                    onClick={goToPrevious}
                    disabled={currentPage === 1}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>

                  {/* Page numbers */}
                  <div className="hidden sm:flex items-center space-x-1">
                    {getPageNumbers().map((pageNum) => (
                      <button
                        key={pageNum}
                        onClick={() => goToPage(pageNum)}
                        className={`px-3 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          currentPage === pageNum
                            ? "bg-blue-600 text-white"
                            : "text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
                        }`}
                      >
                        {pageNum}
                      </button>
                    ))}
                  </div>

                  {/* Mobile page selector */}
                  <div className="sm:hidden">
                    <select
                      value={currentPage}
                      onChange={(e) => goToPage(Number(e.target.value))}
                      className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNum) => (
                        <option key={pageNum} value={pageNum}>
                          Page {pageNum}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Next button */}
                  <button
                    onClick={goToNext}
                    disabled={currentPage === totalPages}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
