package handlers

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"whatsinsight-backend/internal/domain"
	"whatsinsight-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// WhatsAppHandler handles WhatsApp webhook requests
type WhatsAppHandler struct {
	whatsappService *services.WhatsAppService
	webhookSecret   string
}

// NewWhatsAppHandler creates a new WhatsApp handler
func NewWhatsAppHandler(whatsappService *services.WhatsAppService, webhookSecret string) *WhatsAppHandler {
	return &WhatsAppHandler{
		whatsappService: whatsappService,
		webhookSecret:   webhookSecret,
	}
}

// WhatsAppWebhookPayload represents the incoming webhook payload from WhatsApp Business API
type WhatsAppWebhookPayload struct {
	Object string `json:"object"`
	Entry  []struct {
		ID      string `json:"id"`
		Changes []struct {
			Value struct {
				MessagingProduct string `json:"messaging_product"`
				Metadata         struct {
					DisplayPhoneNumber string `json:"display_phone_number"`
					PhoneNumberID      string `json:"phone_number_id"`
				} `json:"metadata"`
				Contacts []struct {
					Profile struct {
						Name string `json:"name"`
					} `json:"profile"`
					WaID string `json:"wa_id"`
				} `json:"contacts,omitempty"`
				Messages []struct {
					From      string `json:"from"`
					ID        string `json:"id"`
					Timestamp string `json:"timestamp"`
					Type      string `json:"type"`
					Text      *struct {
						Body string `json:"body"`
					} `json:"text,omitempty"`
					Image *struct {
						Caption  string `json:"caption,omitempty"`
						MimeType string `json:"mime_type"`
						Sha256   string `json:"sha256"`
						ID       string `json:"id"`
					} `json:"image,omitempty"`
					Video *struct {
						Caption  string `json:"caption,omitempty"`
						MimeType string `json:"mime_type"`
						Sha256   string `json:"sha256"`
						ID       string `json:"id"`
					} `json:"video,omitempty"`
					Audio *struct {
						MimeType string `json:"mime_type"`
						Sha256   string `json:"sha256"`
						ID       string `json:"id"`
						Voice    bool   `json:"voice"`
					} `json:"audio,omitempty"`
					Document *struct {
						Caption  string `json:"caption,omitempty"`
						Filename string `json:"filename,omitempty"`
						MimeType string `json:"mime_type"`
						Sha256   string `json:"sha256"`
						ID       string `json:"id"`
					} `json:"document,omitempty"`
					Sticker *struct {
						MimeType string `json:"mime_type"`
						Sha256   string `json:"sha256"`
						ID       string `json:"id"`
						Animated bool   `json:"animated"`
					} `json:"sticker,omitempty"`
					Location *struct {
						Latitude  float64 `json:"latitude"`
						Longitude float64 `json:"longitude"`
						Name      string  `json:"name,omitempty"`
						Address   string  `json:"address,omitempty"`
					} `json:"location,omitempty"`
					Context *struct {
						From        string `json:"from"`
						ID          string `json:"id"`
						Mentioned   bool   `json:"mentioned,omitempty"`
						Forwarded   bool   `json:"forwarded,omitempty"`
						Frequently  bool   `json:"frequently_forwarded,omitempty"`
					} `json:"context,omitempty"`
				} `json:"messages,omitempty"`
				Statuses []struct {
					ID           string `json:"id"`
					Status       string `json:"status"`
					Timestamp    string `json:"timestamp"`
					RecipientID  string `json:"recipient_id"`
					Conversation struct {
						ID     string `json:"id"`
						Origin struct {
							Type string `json:"type"`
						} `json:"origin"`
					} `json:"conversation,omitempty"`
					Pricing struct {
						Billable     bool   `json:"billable"`
						PricingModel string `json:"pricing_model"`
						Category     string `json:"category"`
					} `json:"pricing,omitempty"`
				} `json:"statuses,omitempty"`
			} `json:"value"`
			Field string `json:"field"`
		} `json:"changes"`
	} `json:"entry"`
}

// HandleWebhook godoc
// @Summary Handle WhatsApp webhook
// @Description Receive and process WhatsApp messages from WhatsApp Business API
// @Tags webhook
// @Accept json
// @Produce json
// @Param X-Hub-Signature-256 header string true "Webhook signature for verification"
// @Param payload body WhatsAppWebhookPayload true "WhatsApp webhook payload"
// @Success 200 {object} domain.APIResponse
// @Failure 400 {object} domain.APIResponse{error=domain.APIError}
// @Failure 401 {object} domain.APIResponse{error=domain.APIError}
// @Failure 500 {object} domain.APIResponse{error=domain.APIError}
// @Router /webhook/whatsapp [post]
func (h *WhatsAppHandler) HandleWebhook(c *gin.Context) {
	// Read the raw body for signature verification
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, domain.APIResponse{
			Success: false,
			Error: &domain.APIError{
				Code:    "INVALID_PAYLOAD",
				Message: "Failed to read request body",
			},
		})
		return
	}

	// Verify webhook signature
	signature := c.GetHeader("X-Hub-Signature-256")
	if !h.verifyWebhookSignature(body, signature) {
		c.JSON(http.StatusUnauthorized, domain.APIResponse{
			Success: false,
			Error: &domain.APIError{
				Code:    "INVALID_SIGNATURE",
				Message: "Webhook signature verification failed",
			},
		})
		return
	}

	// Parse the webhook payload
	var payload WhatsAppWebhookPayload
	if err := json.Unmarshal(body, &payload); err != nil {
		c.JSON(http.StatusBadRequest, domain.APIResponse{
			Success: false,
			Error: &domain.APIError{
				Code:    "INVALID_JSON",
				Message: "Failed to parse webhook payload",
				Details: err.Error(),
			},
		})
		return
	}

	// Validate payload structure
	if payload.Object != "whatsapp_business_account" {
		c.JSON(http.StatusBadRequest, domain.APIResponse{
			Success: false,
			Error: &domain.APIError{
				Code:    "INVALID_OBJECT_TYPE",
				Message: "Expected object type 'whatsapp_business_account'",
			},
		})
		return
	}

	processedMessages := 0
	
	// Process each entry in the webhook
	for _, entry := range payload.Entry {
		for _, change := range entry.Changes {
			if change.Field == "messages" {
				// Process messages
				for _, message := range change.Value.Messages {
					if err := h.processMessage(c, entry.ID, message, change.Value.Contacts); err != nil {
						// Log error but continue processing other messages
						fmt.Printf("Error processing message %s: %v\n", message.ID, err)
						continue
					}
					processedMessages++
				}
			}
		}
	}

	c.JSON(http.StatusOK, domain.APIResponse{
		Success: true,
		Data: map[string]interface{}{
			"message":            "Webhook processed successfully",
			"processed_messages": processedMessages,
		},
	})
}

// verifyWebhookSignature verifies the webhook signature using HMAC-SHA256
func (h *WhatsAppHandler) verifyWebhookSignature(payload []byte, signature string) bool {
	if signature == "" || h.webhookSecret == "" {
		return false
	}

	// Remove "sha256=" prefix if present
	if len(signature) > 7 && signature[:7] == "sha256=" {
		signature = signature[7:]
	}

	// Calculate expected signature
	mac := hmac.New(sha256.New, []byte(h.webhookSecret))
	mac.Write(payload)
	expectedSignature := hex.EncodeToString(mac.Sum(nil))

	// Compare signatures
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// processMessage processes a single WhatsApp message
func (h *WhatsAppHandler) processMessage(c *gin.Context, groupID string, message interface{}, contacts []interface{}) error {
	// Type assertion to get message data
	msgData, ok := message.(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid message format")
	}

	// Extract message fields
	messageID, _ := msgData["id"].(string)
	fromPhone, _ := msgData["from"].(string)
	timestampStr, _ := msgData["timestamp"].(string)
	messageType, _ := msgData["type"].(string)

	if messageID == "" || fromPhone == "" || timestampStr == "" {
		return fmt.Errorf("missing required message fields")
	}

	// Parse timestamp
	timestampInt, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid timestamp format: %v", err)
	}
	timestamp := time.Unix(timestampInt, 0)

	// Extract message content based on type
	var content string
	var mediaURL string

	switch messageType {
	case "text":
		if textData, ok := msgData["text"].(map[string]interface{}); ok {
			content, _ = textData["body"].(string)
		}
	case "image":
		if imageData, ok := msgData["image"].(map[string]interface{}); ok {
			content, _ = imageData["caption"].(string)
			mediaURL, _ = imageData["id"].(string) // Media ID for later download
		}
	case "video":
		if videoData, ok := msgData["video"].(map[string]interface{}); ok {
			content, _ = videoData["caption"].(string)
			mediaURL, _ = videoData["id"].(string)
		}
	case "audio":
		if audioData, ok := msgData["audio"].(map[string]interface{}); ok {
			mediaURL, _ = audioData["id"].(string)
			content = "[Audio Message]"
		}
	case "document":
		if docData, ok := msgData["document"].(map[string]interface{}); ok {
			content, _ = docData["caption"].(string)
			mediaURL, _ = docData["id"].(string)
			if filename, ok := docData["filename"].(string); ok && content == "" {
				content = fmt.Sprintf("[Document: %s]", filename)
			}
		}
	case "sticker":
		content = "[Sticker]"
		if stickerData, ok := msgData["sticker"].(map[string]interface{}); ok {
			mediaURL, _ = stickerData["id"].(string)
		}
	case "location":
		if locData, ok := msgData["location"].(map[string]interface{}); ok {
			lat, _ := locData["latitude"].(float64)
			lng, _ := locData["longitude"].(float64)
			name, _ := locData["name"].(string)
			if name != "" {
				content = fmt.Sprintf("[Location: %s (%.6f, %.6f)]", name, lat, lng)
			} else {
				content = fmt.Sprintf("[Location: %.6f, %.6f]", lat, lng)
			}
		}
	default:
		content = fmt.Sprintf("[%s Message]", messageType)
	}

	// Check for reply context
	var replyToMessageID *uuid.UUID
	if contextData, ok := msgData["context"].(map[string]interface{}); ok {
		if replyID, ok := contextData["id"].(string); ok {
			// Look up the original message ID in our database
			if originalID, err := h.whatsappService.GetMessageIDByWhatsAppID(c.Request.Context(), replyID); err == nil {
				replyToMessageID = &originalID
			}
		}
	}

	// Check if message is forwarded
	isForwarded := false
	if contextData, ok := msgData["context"].(map[string]interface{}); ok {
		isForwarded, _ = contextData["forwarded"].(bool)
		if !isForwarded {
			// Also check for frequently_forwarded
			isForwarded, _ = contextData["frequently_forwarded"].(bool)
		}
	}

	// Create message entity
	messageEntity := &domain.Message{
		WhatsAppMessageID: messageID,
		MessageType:       messageType,
		Content:           content,
		MediaURL:          mediaURL,
		Timestamp:         timestamp,
		ReplyToMessageID:  replyToMessageID,
		IsForwarded:       isForwarded,
	}

	// Process the message through the service layer
	return h.whatsappService.ProcessIncomingMessage(c.Request.Context(), groupID, fromPhone, messageEntity, contacts)
}

// HandleWebhookVerification handles WhatsApp webhook verification (GET request)
func (h *WhatsAppHandler) HandleWebhookVerification(c *gin.Context) {
	// WhatsApp sends a GET request with verification parameters
	mode := c.Query("hub.mode")
	token := c.Query("hub.verify_token")
	challenge := c.Query("hub.challenge")

	// Verify the token (you should set this in your WhatsApp webhook configuration)
	expectedToken := "your_verify_token" // This should come from config
	
	if mode == "subscribe" && token == expectedToken {
		// Respond with the challenge to verify the webhook
		c.String(http.StatusOK, challenge)
		return
	}

	c.JSON(http.StatusForbidden, domain.APIResponse{
		Success: false,
		Error: &domain.APIError{
			Code:    "VERIFICATION_FAILED",
			Message: "Webhook verification failed",
		},
	})
}
