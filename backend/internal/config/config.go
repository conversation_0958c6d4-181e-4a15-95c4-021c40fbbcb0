package config

import (
	"fmt"
	"strings"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Redis    RedisConfig    `mapstructure:"redis"`
	JWT      JWTConfig      `mapstructure:"jwt"`
	WhatsApp WhatsAppConfig `mapstructure:"whatsapp"`
	Analytics AnalyticsConfig `mapstructure:"analytics"`
}

// ServerConfig represents server configuration
type ServerConfig struct {
	Port         int    `mapstructure:"port"`
	Environment  string `mapstructure:"environment"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
	IdleTimeout  int    `mapstructure:"idle_timeout"`
}

// DatabaseConfig represents database configuration
type DatabaseConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	DBName   string `mapstructure:"dbname"`
	SSLMode  string `mapstructure:"sslmode"`
	TimeZone string `mapstructure:"timezone"`
}

// RedisConfig represents Redis configuration
type RedisConfig struct {
	Address  string `mapstructure:"address"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

// JWTConfig represents JWT configuration
type JWTConfig struct {
	Secret     string `mapstructure:"secret"`
	ExpiryHours int   `mapstructure:"expiry_hours"`
}

// WhatsAppConfig represents WhatsApp API configuration
type WhatsAppConfig struct {
	APIBaseURL    string `mapstructure:"api_base_url"`
	AccessToken   string `mapstructure:"access_token"`
	WebhookSecret string `mapstructure:"webhook_secret"`
}

// AnalyticsConfig represents analytics processing configuration
type AnalyticsConfig struct {
	BatchSize           int `mapstructure:"batch_size"`
	ProcessingInterval  int `mapstructure:"processing_interval"` // minutes
	SentimentAPIURL     string `mapstructure:"sentiment_api_url"`
	SentimentAPIKey     string `mapstructure:"sentiment_api_key"`
	WordCloudMaxWords   int `mapstructure:"word_cloud_max_words"`
	EmojiMaxResults     int `mapstructure:"emoji_max_results"`
}

// Load loads the configuration from environment variables and config files
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")
	viper.AddConfigPath("/etc/whatsinsight")

	// Set default values
	setDefaults()

	// Enable environment variable support
	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Read config file (optional)
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("error reading config file: %w", err)
		}
		// Config file not found, continue with environment variables and defaults
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("error unmarshaling config: %w", err)
	}

	// Validate required configuration
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// Server defaults
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.environment", "development")
	viper.SetDefault("server.read_timeout", 30)
	viper.SetDefault("server.write_timeout", 30)
	viper.SetDefault("server.idle_timeout", 120)

	// Database defaults
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.user", "whatsinsight")
	viper.SetDefault("database.password", "password")
	viper.SetDefault("database.dbname", "whatsinsight")
	viper.SetDefault("database.sslmode", "disable")
	viper.SetDefault("database.timezone", "UTC")

	// Redis defaults
	viper.SetDefault("redis.address", "localhost:6379")
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 0)

	// JWT defaults
	viper.SetDefault("jwt.secret", "your-secret-key-change-in-production")
	viper.SetDefault("jwt.expiry_hours", 24)

	// WhatsApp defaults
	viper.SetDefault("whatsapp.api_base_url", "https://graph.facebook.com/v18.0")
	viper.SetDefault("whatsapp.access_token", "")
	viper.SetDefault("whatsapp.webhook_secret", "")

	// Analytics defaults
	viper.SetDefault("analytics.batch_size", 1000)
	viper.SetDefault("analytics.processing_interval", 5)
	viper.SetDefault("analytics.sentiment_api_url", "")
	viper.SetDefault("analytics.sentiment_api_key", "")
	viper.SetDefault("analytics.word_cloud_max_words", 100)
	viper.SetDefault("analytics.emoji_max_results", 20)
}

// validateConfig validates the configuration
func validateConfig(config *Config) error {
	// Validate required database fields
	if config.Database.Host == "" {
		return fmt.Errorf("database host is required")
	}
	if config.Database.User == "" {
		return fmt.Errorf("database user is required")
	}
	if config.Database.DBName == "" {
		return fmt.Errorf("database name is required")
	}

	// Validate JWT secret in production
	if config.Server.Environment == "production" && config.JWT.Secret == "your-secret-key-change-in-production" {
		return fmt.Errorf("JWT secret must be changed in production")
	}

	// Validate server port
	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", config.Server.Port)
	}

	return nil
}

// GetDSN returns the database connection string
func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
		c.Host, c.Port, c.User, c.Password, c.DBName, c.SSLMode, c.TimeZone)
}
