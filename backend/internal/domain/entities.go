package domain

import (
	"time"

	"github.com/google/uuid"
)

// User represents a system user/admin
type User struct {
	ID           uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	Email        string    `json:"email" gorm:"uniqueIndex;not null"`
	PasswordHash string    `json:"-" gorm:"not null"`
	FullName     string    `json:"full_name" gorm:"not null"`
	Role         string    `json:"role" gorm:"default:'admin'"`
	IsActive     bool      `json:"is_active" gorm:"default:true"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// WhatsAppGroup represents a WhatsApp group/community
type WhatsAppGroup struct {
	ID           uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	GroupID      string    `json:"group_id" gorm:"uniqueIndex;not null"` // WhatsApp Group ID
	Name         string    `json:"name" gorm:"not null"`
	Description  string    `json:"description"`
	AvatarURL    string    `json:"avatar_url"`
	MemberCount  int       `json:"member_count" gorm:"default:0"`
	IsActive     bool      `json:"is_active" gorm:"default:true"`
	WebhookToken string    `json:"webhook_token" gorm:"uniqueIndex"` // For webhook security
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`

	// Relationships
	Members []GroupMember `json:"members,omitempty" gorm:"foreignKey:GroupID"`
}

// GroupMember represents a member of a WhatsApp group
type GroupMember struct {
	ID              uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	GroupID         uuid.UUID `json:"group_id" gorm:"not null"`
	WhatsAppUserID  string    `json:"whatsapp_user_id" gorm:"not null"`
	PhoneNumber     string    `json:"phone_number"`
	DisplayName     string    `json:"display_name" gorm:"not null"`
	AvatarURL       string    `json:"avatar_url"`
	JoinedAt        time.Time `json:"joined_at"`
	IsActive        bool      `json:"is_active" gorm:"default:true"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`

	// Relationships
	Group WhatsAppGroup `json:"group,omitempty" gorm:"foreignKey:GroupID"`
}

// Message represents a WhatsApp message
type Message struct {
	ID                 uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	GroupID            uuid.UUID  `json:"group_id" gorm:"not null"`
	MemberID           uuid.UUID  `json:"member_id" gorm:"not null"`
	WhatsAppMessageID  string     `json:"whatsapp_message_id" gorm:"uniqueIndex;not null"`
	MessageType        string     `json:"message_type" gorm:"default:'text'"`
	Content            string     `json:"content"`
	MediaURL           string     `json:"media_url"`
	Timestamp          time.Time  `json:"timestamp" gorm:"not null"`
	ReplyToMessageID   *uuid.UUID `json:"reply_to_message_id"`
	IsForwarded        bool       `json:"is_forwarded" gorm:"default:false"`
	CreatedAt          time.Time  `json:"created_at"`

	// Relationships
	Group     WhatsAppGroup     `json:"group,omitempty" gorm:"foreignKey:GroupID"`
	Member    GroupMember       `json:"member,omitempty" gorm:"foreignKey:MemberID"`
	Reactions []MessageReaction `json:"reactions,omitempty" gorm:"foreignKey:MessageID"`
	Sentiment *SentimentAnalysis `json:"sentiment,omitempty" gorm:"foreignKey:MessageID"`
}

// MessageReaction represents a reaction to a message
type MessageReaction struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	MessageID uuid.UUID `json:"message_id" gorm:"not null"`
	MemberID  uuid.UUID `json:"member_id" gorm:"not null"`
	Emoji     string    `json:"emoji" gorm:"not null"`
	CreatedAt time.Time `json:"created_at"`

	// Relationships
	Message Message     `json:"message,omitempty" gorm:"foreignKey:MessageID"`
	Member  GroupMember `json:"member,omitempty" gorm:"foreignKey:MemberID"`
}

// DailyActivity represents aggregated daily activity data
type DailyActivity struct {
	ID            uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	GroupID       uuid.UUID `json:"group_id" gorm:"not null"`
	Date          time.Time `json:"date" gorm:"type:date;not null"`
	TotalMessages int       `json:"total_messages" gorm:"default:0"`
	TotalReactions int      `json:"total_reactions" gorm:"default:0"`
	ActiveMembers int       `json:"active_members" gorm:"default:0"`
	PeakHour      *int      `json:"peak_hour"` // Hour with most activity (0-23)
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`

	// Relationships
	Group WhatsAppGroup `json:"group,omitempty" gorm:"foreignKey:GroupID"`
}

// HourlyMessageVolume represents hourly message volume data
type HourlyMessageVolume struct {
	ID           uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	GroupID      uuid.UUID `json:"group_id" gorm:"not null"`
	Date         time.Time `json:"date" gorm:"type:date;not null"`
	Hour         int       `json:"hour" gorm:"check:hour >= 0 AND hour <= 23"`
	MessageCount int       `json:"message_count" gorm:"default:0"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`

	// Relationships
	Group WhatsAppGroup `json:"group,omitempty" gorm:"foreignKey:GroupID"`
}

// MemberActivityScore represents member activity scores
type MemberActivityScore struct {
	ID            uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	GroupID       uuid.UUID `json:"group_id" gorm:"not null"`
	MemberID      uuid.UUID `json:"member_id" gorm:"not null"`
	PeriodStart   time.Time `json:"period_start" gorm:"type:date;not null"`
	PeriodEnd     time.Time `json:"period_end" gorm:"type:date;not null"`
	MessageCount  int       `json:"message_count" gorm:"default:0"`
	ReactionCount int       `json:"reaction_count" gorm:"default:0"`
	ActivityScore float64   `json:"activity_score" gorm:"type:decimal(5,2);default:0.00"`
	Status        string    `json:"status" gorm:"default:'Low'"`
	RankPosition  *int      `json:"rank_position"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`

	// Relationships
	Group  WhatsAppGroup `json:"group,omitempty" gorm:"foreignKey:GroupID"`
	Member GroupMember   `json:"member,omitempty" gorm:"foreignKey:MemberID"`
}

// WordFrequency represents word usage frequency
type WordFrequency struct {
	ID         uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	GroupID    uuid.UUID `json:"group_id" gorm:"not null"`
	Word       string    `json:"word" gorm:"not null"`
	Frequency  int       `json:"frequency" gorm:"default:1"`
	LastUsedAt time.Time `json:"last_used_at"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`

	// Relationships
	Group WhatsAppGroup `json:"group,omitempty" gorm:"foreignKey:GroupID"`
}

// EmojiUsage represents emoji usage tracking
type EmojiUsage struct {
	ID         uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	GroupID    uuid.UUID `json:"group_id" gorm:"not null"`
	Emoji      string    `json:"emoji" gorm:"not null"`
	EmojiName  string    `json:"emoji_name"`
	UsageCount int       `json:"usage_count" gorm:"default:1"`
	LastUsedAt time.Time `json:"last_used_at"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`

	// Relationships
	Group WhatsAppGroup `json:"group,omitempty" gorm:"foreignKey:GroupID"`
}

// SentimentAnalysis represents sentiment analysis results
type SentimentAnalysis struct {
	ID              uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:uuid_generate_v4()"`
	MessageID       uuid.UUID `json:"message_id" gorm:"not null"`
	Sentiment       string    `json:"sentiment" gorm:"check:sentiment IN ('positive', 'neutral', 'negative')"`
	ConfidenceScore float64   `json:"confidence_score" gorm:"type:decimal(4,3)"` // 0.000 to 1.000
	ProcessedAt     time.Time `json:"processed_at"`

	// Relationships
	Message Message `json:"message,omitempty" gorm:"foreignKey:MessageID"`
}

// DTO structs for API responses

// TimelineDataPoint represents a point in timeline analytics
type TimelineDataPoint struct {
	Period        string  `json:"period"`
	TotalMessages int     `json:"total_messages"`
	TotalReactions int    `json:"total_reactions"`
	ActiveMembers int     `json:"active_members"`
	ActivityScore float64 `json:"activity_score"`
}

// BusyDayData represents busy day analytics
type BusyDayData struct {
	DayOfWeek        string  `json:"day_of_week"`
	DayNumber        int     `json:"day_number"`
	AvgMessages      float64 `json:"avg_messages"`
	AvgActivityScore float64 `json:"avg_activity_score"`
	TotalDaysAnalyzed int    `json:"total_days_analyzed"`
}

// HourlyVolumeData represents hourly message volume
type HourlyVolumeData struct {
	Hour         int    `json:"hour"`
	HourLabel    string `json:"hour_label"`
	AvgMessages  float64 `json:"avg_messages"`
	PeakDay      string `json:"peak_day"`
	PeakMessages int    `json:"peak_messages"`
}

// TopContributorData represents top contributor information
type TopContributorData struct {
	Rank          int     `json:"rank"`
	MemberID      uuid.UUID `json:"member_id"`
	DisplayName   string  `json:"display_name"`
	AvatarURL     string  `json:"avatar_url"`
	MessageCount  int     `json:"message_count"`
	ReactionCount int     `json:"reaction_count"`
	ActivityScore float64 `json:"activity_score"`
	Status        string  `json:"status"`
}

// WordCloudData represents word frequency for word cloud
type WordCloudData struct {
	Word         string  `json:"word"`
	Frequency    int     `json:"frequency"`
	RelativeSize float64 `json:"relative_size"`
}

// EmojiFrequencyData represents emoji usage frequency
type EmojiFrequencyData struct {
	Rank       int     `json:"rank"`
	Emoji      string  `json:"emoji"`
	EmojiName  string  `json:"emoji_name"`
	UsageCount int     `json:"usage_count"`
	Percentage float64 `json:"percentage"`
}

// SentimentData represents sentiment analysis breakdown
type SentimentData struct {
	Positive SentimentCategory `json:"positive"`
	Neutral  SentimentCategory `json:"neutral"`
	Negative SentimentCategory `json:"negative"`
}

// SentimentCategory represents a sentiment category
type SentimentCategory struct {
	Count      int     `json:"count"`
	Percentage float64 `json:"percentage"`
	Color      string  `json:"color"`
}

// RecentMessageData represents recent message information
type RecentMessageData struct {
	ID          uuid.UUID           `json:"id"`
	Member      MemberInfo          `json:"member"`
	Content     string              `json:"content"`
	MessageType string              `json:"message_type"`
	Timestamp   time.Time           `json:"timestamp"`
	Reactions   []ReactionSummary   `json:"reactions"`
}

// MemberInfo represents basic member information
type MemberInfo struct {
	DisplayName string `json:"display_name"`
	AvatarURL   string `json:"avatar_url"`
}

// ReactionSummary represents reaction summary
type ReactionSummary struct {
	Emoji string `json:"emoji"`
	Count int    `json:"count"`
}

// GroupStats represents basic group statistics
type GroupStats struct {
	TotalMembers      int       `json:"total_members"`
	TotalMessages     int       `json:"total_messages"`
	AvgDailyMessages  float64   `json:"avg_daily_messages"`
	MostActiveMember  string    `json:"most_active_member"`
	GroupAgeDays      int       `json:"group_age_days"`
	LastActivity      time.Time `json:"last_activity"`
}

// Pagination represents pagination metadata
type Pagination struct {
	CurrentPage   int `json:"current_page"`
	TotalPages    int `json:"total_pages"`
	TotalItems    int `json:"total_items"`
	ItemsPerPage  int `json:"items_per_page"`
}

// APIResponse represents a standard API response
type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   *APIError   `json:"error,omitempty"`
	Meta    interface{} `json:"meta,omitempty"`
	Pagination *Pagination `json:"pagination,omitempty"`
}

// APIError represents an API error
type APIError struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Details interface{} `json:"details,omitempty"`
}
