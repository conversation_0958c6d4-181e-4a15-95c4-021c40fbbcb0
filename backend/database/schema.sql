-- WhatsInsight Database Schema
-- PostgreSQL 15+ with TimescaleDB extension for time-series optimization

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "timescaledb";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For text search optimization

-- =====================================================
-- CORE ENTITIES
-- =====================================================

-- Users/Admins table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'admin' CHECK (role IN ('admin', 'viewer')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- WhatsApp Groups/Communities
CREATE TABLE whatsapp_groups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id VARCHAR(255) UNIQUE NOT NULL, -- WhatsApp Group ID
    name VARCHAR(255) NOT NULL,
    description TEXT,
    avatar_url VARCHAR(500),
    member_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    webhook_token VARCHAR(255) UNIQUE, -- For webhook security
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Group Members
CREATE TABLE group_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID REFERENCES whatsapp_groups(id) ON DELETE CASCADE,
    whatsapp_user_id VARCHAR(255) NOT NULL, -- WhatsApp User ID
    phone_number VARCHAR(20),
    display_name VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(group_id, whatsapp_user_id)
);

-- =====================================================
-- MESSAGE DATA (Time-series optimized)
-- =====================================================

-- Main messages table (TimescaleDB hypertable)
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID REFERENCES whatsapp_groups(id) ON DELETE CASCADE,
    member_id UUID REFERENCES group_members(id) ON DELETE CASCADE,
    whatsapp_message_id VARCHAR(255) UNIQUE NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'video', 'audio', 'document', 'sticker', 'location', 'contact')),
    content TEXT,
    media_url VARCHAR(500),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    reply_to_message_id UUID REFERENCES messages(id),
    is_forwarded BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Convert to TimescaleDB hypertable for time-series optimization
SELECT create_hypertable('messages', 'timestamp', chunk_time_interval => INTERVAL '1 day');

-- Message reactions
CREATE TABLE message_reactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID REFERENCES messages(id) ON DELETE CASCADE,
    member_id UUID REFERENCES group_members(id) ON DELETE CASCADE,
    emoji VARCHAR(10) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(message_id, member_id, emoji)
);

-- =====================================================
-- ANALYTICS TABLES
-- =====================================================

-- Daily activity aggregates (for performance)
CREATE TABLE daily_activity (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID REFERENCES whatsapp_groups(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    total_messages INTEGER DEFAULT 0,
    total_reactions INTEGER DEFAULT 0,
    active_members INTEGER DEFAULT 0,
    peak_hour INTEGER, -- Hour with most activity (0-23)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(group_id, date)
);

-- Hourly message volume (for hourly analytics)
CREATE TABLE hourly_message_volume (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID REFERENCES whatsapp_groups(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    hour INTEGER CHECK (hour >= 0 AND hour <= 23),
    message_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(group_id, date, hour)
);

-- Member activity scores (updated periodically)
CREATE TABLE member_activity_scores (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID REFERENCES whatsapp_groups(id) ON DELETE CASCADE,
    member_id UUID REFERENCES group_members(id) ON DELETE CASCADE,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    message_count INTEGER DEFAULT 0,
    reaction_count INTEGER DEFAULT 0,
    activity_score DECIMAL(5,2) DEFAULT 0.00, -- 0-100 scale
    status VARCHAR(20) DEFAULT 'Low' CHECK (status IN ('Very Active', 'Active', 'Moderate', 'Low')),
    rank_position INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(group_id, member_id, period_start, period_end)
);

-- Word frequency tracking
CREATE TABLE word_frequency (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID REFERENCES whatsapp_groups(id) ON DELETE CASCADE,
    word VARCHAR(100) NOT NULL,
    frequency INTEGER DEFAULT 1,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(group_id, word)
);

-- Emoji usage tracking
CREATE TABLE emoji_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID REFERENCES whatsapp_groups(id) ON DELETE CASCADE,
    emoji VARCHAR(10) NOT NULL,
    emoji_name VARCHAR(100),
    usage_count INTEGER DEFAULT 1,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(group_id, emoji)
);

-- Sentiment analysis results
CREATE TABLE sentiment_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID REFERENCES messages(id) ON DELETE CASCADE,
    sentiment VARCHAR(20) CHECK (sentiment IN ('positive', 'neutral', 'negative')),
    confidence_score DECIMAL(4,3), -- 0.000 to 1.000
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Messages table indexes
CREATE INDEX idx_messages_group_timestamp ON messages(group_id, timestamp DESC);
CREATE INDEX idx_messages_member_timestamp ON messages(member_id, timestamp DESC);
CREATE INDEX idx_messages_type ON messages(message_type);
CREATE INDEX idx_messages_timestamp ON messages(timestamp DESC);

-- Group members indexes
CREATE INDEX idx_group_members_group_id ON group_members(group_id);
CREATE INDEX idx_group_members_whatsapp_id ON group_members(whatsapp_user_id);

-- Daily activity indexes
CREATE INDEX idx_daily_activity_group_date ON daily_activity(group_id, date DESC);

-- Hourly volume indexes
CREATE INDEX idx_hourly_volume_group_date_hour ON hourly_message_volume(group_id, date DESC, hour);

-- Member activity scores indexes
CREATE INDEX idx_member_activity_group_period ON member_activity_scores(group_id, period_start DESC, period_end DESC);
CREATE INDEX idx_member_activity_score ON member_activity_scores(activity_score DESC);

-- Word frequency indexes
CREATE INDEX idx_word_frequency_group ON word_frequency(group_id, frequency DESC);
CREATE INDEX idx_word_frequency_word ON word_frequency(word);

-- Emoji usage indexes
CREATE INDEX idx_emoji_usage_group ON emoji_usage(group_id, usage_count DESC);

-- Sentiment analysis indexes
CREATE INDEX idx_sentiment_message ON sentiment_analysis(message_id);
CREATE INDEX idx_sentiment_type ON sentiment_analysis(sentiment);

-- Text search indexes
CREATE INDEX idx_messages_content_gin ON messages USING gin(content gin_trgm_ops);
CREATE INDEX idx_word_frequency_word_gin ON word_frequency USING gin(word gin_trgm_ops);

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update member count in groups
CREATE OR REPLACE FUNCTION update_group_member_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE whatsapp_groups 
        SET member_count = member_count + 1 
        WHERE id = NEW.group_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE whatsapp_groups 
        SET member_count = member_count - 1 
        WHERE id = OLD.group_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger for member count updates
CREATE TRIGGER trigger_update_group_member_count
    AFTER INSERT OR DELETE ON group_members
    FOR EACH ROW EXECUTE FUNCTION update_group_member_count();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER trigger_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_groups_updated_at BEFORE UPDATE ON whatsapp_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_members_updated_at BEFORE UPDATE ON group_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_daily_activity_updated_at BEFORE UPDATE ON daily_activity FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_hourly_volume_updated_at BEFORE UPDATE ON hourly_message_volume FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_member_scores_updated_at BEFORE UPDATE ON member_activity_scores FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_word_frequency_updated_at BEFORE UPDATE ON word_frequency FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_emoji_usage_updated_at BEFORE UPDATE ON emoji_usage FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
