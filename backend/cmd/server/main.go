package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"whatsinsight-backend/internal/config"
	"whatsinsight-backend/internal/database"
	"whatsinsight-backend/internal/handlers"
	"whatsinsight-backend/internal/middleware"
	"whatsinsight-backend/internal/repositories"
	"whatsinsight-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// @title WhatsInsight API
// @version 1.0
// @description WhatsApp Group Analytics & Management Platform API
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.whatsinsight.com/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database
	db, err := database.Initialize(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Initialize Redis
	redisClient := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.Address,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})

	// Test Redis connection
	ctx := context.Background()
	if err := redisClient.Ping(ctx).Err(); err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}

	// Initialize repositories
	repos := repositories.NewRepositories(db, redisClient)

	// Initialize services
	services := services.NewServices(repos, cfg)

	// Initialize handlers
	handlers := handlers.NewHandlers(services)

	// Setup Gin router
	if cfg.Server.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Global middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS())
	router.Use(middleware.RequestID())
	router.Use(middleware.RateLimiter(redisClient))

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"timestamp": time.Now().UTC(),
			"version":   "1.0.0",
		})
	})

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Public routes (no authentication required)
		public := v1.Group("")
		{
			// WhatsApp webhook (secured by webhook token)
			public.POST("/webhook/whatsapp", handlers.WhatsApp.HandleWebhook)
		}

		// Protected routes (require authentication)
		protected := v1.Group("")
		protected.Use(middleware.JWTAuth(cfg.JWT.Secret))
		{
			// Group management
			groups := protected.Group("/groups")
			{
				groups.GET("", handlers.Groups.GetGroups)
				groups.GET("/:group_id/stats", handlers.Groups.GetGroupStats)
			}

			// Analytics endpoints
			analytics := protected.Group("/analytics")
			{
				// Timeline analytics
				timeline := analytics.Group("/timeline")
				{
					timeline.GET("/daily", handlers.Analytics.GetDailyTimeline)
					timeline.GET("/weekly", handlers.Analytics.GetWeeklyTimeline)
					timeline.GET("/monthly", handlers.Analytics.GetMonthlyTimeline)
				}

				// Activity pattern analytics
				activity := analytics.Group("/activity")
				{
					activity.GET("/busy-days", handlers.Analytics.GetBusyDays)
					activity.GET("/busy-months", handlers.Analytics.GetBusyMonths)
				}

				// Message analytics
				messages := analytics.Group("/messages")
				{
					messages.GET("/by-hour", handlers.Analytics.GetMessagesByHour)
				}

				// Member analytics
				contributors := analytics.Group("/contributors")
				{
					contributors.GET("/top", handlers.Analytics.GetTopContributors)
				}

				members := analytics.Group("/members")
				{
					members.GET("/leaderboard", handlers.Analytics.GetMemberLeaderboard)
				}

				// Content analytics
				words := analytics.Group("/words")
				{
					words.GET("/cloud", handlers.Analytics.GetWordCloud)
				}

				emojis := analytics.Group("/emojis")
				{
					emojis.GET("/frequent", handlers.Analytics.GetFrequentEmojis)
				}

				// Sentiment analytics
				analytics.GET("/sentiment", handlers.Analytics.GetSentimentAnalysis)
			}

			// Recent activity
			messages := protected.Group("/messages")
			{
				messages.GET("/recent", handlers.Messages.GetRecentMessages)
			}
		}
	}

	// Swagger documentation (only in development)
	if cfg.Server.Environment != "production" {
		router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	}

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting server on port %d", cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	// Close database connection
	sqlDB, err := db.DB()
	if err == nil {
		sqlDB.Close()
	}

	// Close Redis connection
	redisClient.Close()

	log.Println("Server exited")
}
