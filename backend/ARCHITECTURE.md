# WhatsInsight Backend Architecture

## 🏗️ High-Level Architecture Overview

WhatsInsight follows **Clean Architecture** principles with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Interface Layer                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   HTTP Handlers │  │   Middleware    │  │  WebSocket  │ │
│  │   (Gin Router)  │  │   (Auth, CORS)  │  │  (Future)   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  Application Layer                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │    Services     │  │   Use Cases     │  │   DTOs      │ │
│  │  (Business      │  │  (Analytics,    │  │ (Request/   │ │
│  │   Logic)        │  │   Webhooks)     │  │  Response)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Domain Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │    Entities     │  │   Interfaces    │  │   Value     │ │
│  │   (Models)      │  │ (Repositories)  │  │  Objects    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                Infrastructure Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Database      │  │     Redis       │  │  External   │ │
│  │ (PostgreSQL +   │  │   (Caching)     │  │    APIs     │ │
│  │  TimescaleDB)   │  │                 │  │ (WhatsApp)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Tech Stack

### Core Framework & Libraries
- **Go 1.24**: Latest Go version with improved performance
- **Gin**: Lightweight, fast HTTP web framework
- **GORM v2**: Feature-rich ORM with excellent PostgreSQL support
- **PostgreSQL 15+**: Primary database with JSONB support
- **TimescaleDB**: Time-series extension for message analytics
- **Redis**: Caching, rate limiting, and session management

### Additional Tools
- **JWT**: Authentication and authorization
- **Swagger/OpenAPI**: API documentation
- **Viper**: Configuration management
- **Logrus/Zap**: Structured logging
- **Prometheus**: Metrics and monitoring
- **Docker**: Containerization

## 📁 Project Structure

```
backend/
├── cmd/
│   └── server/
│       └── main.go                 # Application entry point
├── internal/
│   ├── config/
│   │   └── config.go              # Configuration management
│   ├── domain/
│   │   ├── entities.go            # Domain entities and DTOs
│   │   └── interfaces.go          # Repository interfaces
│   ├── handlers/
│   │   ├── analytics.go           # Analytics HTTP handlers
│   │   ├── groups.go              # Group management handlers
│   │   ├── messages.go            # Message handlers
│   │   ├── whatsapp.go            # WhatsApp webhook handler
│   │   └── handlers.go            # Handler initialization
│   ├── services/
│   │   ├── analytics.go           # Analytics business logic
│   │   ├── whatsapp.go            # WhatsApp integration
│   │   ├── sentiment.go           # Sentiment analysis
│   │   └── services.go            # Service initialization
│   ├── repositories/
│   │   ├── analytics.go           # Analytics data access
│   │   ├── groups.go              # Group data access
│   │   ├── messages.go            # Message data access
│   │   └── repositories.go        # Repository initialization
│   ├── middleware/
│   │   ├── auth.go                # JWT authentication
│   │   ├── cors.go                # CORS handling
│   │   ├── rate_limiter.go        # Rate limiting
│   │   └── request_id.go          # Request ID tracking
│   ├── database/
│   │   ├── migrations/            # Database migrations
│   │   └── database.go            # Database initialization
│   └── utils/
│       ├── jwt.go                 # JWT utilities
│       ├── validation.go          # Input validation
│       └── time.go                # Time utilities
├── database/
│   └── schema.sql                 # Database schema
├── api/
│   └── endpoints.md               # API documentation
├── config/
│   └── config.yaml                # Configuration file
├── docker/
│   ├── Dockerfile                 # Application container
│   └── docker-compose.yml         # Development environment
├── docs/                          # Swagger documentation
├── go.mod                         # Go module definition
├── go.sum                         # Go module checksums
├── Makefile                       # Build and development commands
└── README.md                      # Project documentation
```

## 🚀 Performance & Scalability Considerations

### 1. Database Optimization

#### TimescaleDB for Time-Series Data
- **Hypertables**: Messages table partitioned by timestamp for optimal time-series queries
- **Automatic Partitioning**: Data automatically partitioned by day/week
- **Compression**: Older data compressed to save storage
- **Continuous Aggregates**: Pre-computed aggregations for common queries

#### Indexing Strategy
```sql
-- Time-series optimized indexes
CREATE INDEX idx_messages_group_timestamp ON messages(group_id, timestamp DESC);
CREATE INDEX idx_messages_member_timestamp ON messages(member_id, timestamp DESC);

-- Analytics-specific indexes
CREATE INDEX idx_daily_activity_group_date ON daily_activity(group_id, date DESC);
CREATE INDEX idx_hourly_volume_group_date_hour ON hourly_message_volume(group_id, date DESC, hour);

-- Full-text search indexes
CREATE INDEX idx_messages_content_gin ON messages USING gin(content gin_trgm_ops);
```

#### Query Optimization
- **Materialized Views**: Pre-computed analytics for common date ranges
- **Partial Indexes**: Indexes on active groups only
- **Connection Pooling**: Optimized database connection management

### 2. Caching Strategy

#### Redis Caching Layers
```go
// Cache hierarchy (TTL in seconds)
analytics_cache_key := fmt.Sprintf("analytics:%s:%s:%s", groupID, startDate, endDate)
// Level 1: Hot data (5 minutes)
// Level 2: Warm data (1 hour)  
// Level 3: Cold data (24 hours)
```

#### Cache Invalidation
- **Time-based**: Automatic expiration for time-sensitive data
- **Event-based**: Cache invalidation on new messages
- **Lazy Loading**: Cache miss triggers background refresh

### 3. Background Processing

#### Message Processing Pipeline
```go
// Async processing workflow
WhatsApp Webhook → Redis Queue → Background Workers → Database
                                      ↓
                              Analytics Aggregation
                                      ↓
                              Cache Warming
```

#### Worker Pools
- **Message Ingestion**: High-priority workers for real-time data
- **Analytics Processing**: Lower-priority workers for aggregations
- **Sentiment Analysis**: Separate workers for ML processing

### 4. API Performance

#### Rate Limiting
```go
// Rate limits per endpoint type
Analytics Endpoints: 100 requests/minute per user
Webhook Endpoints: 1000 requests/minute per group
Management Endpoints: 50 requests/minute per user
```

#### Response Optimization
- **Pagination**: All list endpoints support pagination
- **Field Selection**: Optional field filtering to reduce payload size
- **Compression**: Gzip compression for large responses
- **HTTP/2**: Support for multiplexed connections

### 5. Horizontal Scaling

#### Stateless Design
- **No Server State**: All state stored in database/cache
- **Load Balancer Ready**: Multiple instances can run behind load balancer
- **Database Sharding**: Future support for horizontal database scaling

#### Microservice Readiness
```
Current Monolith → Future Microservices
├── Analytics Service
├── Message Processing Service  
├── WhatsApp Integration Service
└── User Management Service
```

## 🔒 Security Considerations

### 1. Webhook Security
```go
// WhatsApp webhook validation
func validateWebhookSignature(payload []byte, signature string, secret string) bool {
    expectedSignature := hmac.New(sha256.New, []byte(secret))
    expectedSignature.Write(payload)
    return hmac.Equal([]byte(signature), expectedSignature.Sum(nil))
}
```

### 2. Authentication & Authorization
- **JWT Tokens**: Stateless authentication with configurable expiry
- **Role-based Access**: Admin vs Viewer permissions
- **API Key Authentication**: For webhook endpoints

### 3. Data Protection
- **Input Validation**: All inputs validated and sanitized
- **SQL Injection Prevention**: Parameterized queries via GORM
- **Rate Limiting**: Protection against abuse
- **CORS Configuration**: Proper cross-origin resource sharing

## 📊 Monitoring & Observability

### 1. Metrics Collection
```go
// Prometheus metrics
var (
    httpRequestsTotal = prometheus.NewCounterVec(...)
    httpRequestDuration = prometheus.NewHistogramVec(...)
    databaseQueryDuration = prometheus.NewHistogramVec(...)
    cacheHitRate = prometheus.NewGaugeVec(...)
)
```

### 2. Logging Strategy
- **Structured Logging**: JSON format for easy parsing
- **Request Tracing**: Unique request IDs for debugging
- **Error Tracking**: Comprehensive error logging with context

### 3. Health Checks
```go
// Health check endpoints
GET /health           # Basic health check
GET /health/detailed  # Detailed system status
GET /metrics          # Prometheus metrics
```

## 🔄 Data Flow Architecture

### 1. Message Ingestion Flow
```
WhatsApp Business API → Webhook → Validation → Queue → Processing → Storage
                                     ↓
                              Real-time Analytics Update
```

### 2. Analytics Query Flow
```
API Request → Cache Check → Database Query → Aggregation → Cache Store → Response
```

### 3. Background Processing Flow
```
Scheduled Jobs → Data Aggregation → Cache Warming → Notification (if needed)
```

This architecture provides a solid foundation for the WhatsInsight platform with excellent performance, scalability, and maintainability characteristics.
