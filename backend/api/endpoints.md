# WhatsInsight API Endpoints Specification

## Core Analytics Endpoints

### 1. Timeline Analytics

#### GET /api/v1/analytics/timeline/daily
**Purpose**: Get daily activity timeline data
**Query Parameters**:
- `group_id` (UUID, required): WhatsApp group ID
- `start_date` (string, required): Start date (YYYY-MM-DD)
- `end_date` (string, required): End date (YYYY-MM-DD)
- `limit` (int, optional): Max results (default: 365)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "date": "2024-01-01",
      "total_messages": 45,
      "total_reactions": 12,
      "active_members": 8,
      "activity_score": 75.5
    }
  ],
  "meta": {
    "total_days": 30,
    "avg_daily_messages": 42.3
  }
}
```

#### GET /api/v1/analytics/timeline/weekly
**Purpose**: Get weekly aggregated timeline data
**Query Parameters**: Same as daily
**Response**: Similar structure with weekly aggregation

#### GET /api/v1/analytics/timeline/monthly
**Purpose**: Get monthly aggregated timeline data
**Query Parameters**: Same as daily
**Response**: Similar structure with monthly aggregation

### 2. Activity Pattern Analytics

#### GET /api/v1/analytics/activity/busy-days
**Purpose**: Get most busy days of the week
**Query Parameters**:
- `group_id` (UUID, required)
- `start_date`, `end_date` (string, required)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "day_of_week": "Monday",
      "day_number": 1,
      "avg_messages": 67.5,
      "avg_activity_score": 82.3,
      "total_days_analyzed": 12
    }
  ]
}
```

#### GET /api/v1/analytics/activity/busy-months
**Purpose**: Get most busy months of the year
**Query Parameters**: Same as busy-days
**Response**: Similar structure with monthly data

#### GET /api/v1/analytics/messages/by-hour
**Purpose**: Get message volume by hour of day
**Query Parameters**:
- `group_id` (UUID, required)
- `start_date`, `end_date` (string, required)

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "hour": 9,
      "hour_label": "09:00",
      "avg_messages": 15.2,
      "peak_day": "2024-01-15",
      "peak_messages": 45
    }
  ]
}
```

### 3. Member Analytics

#### GET /api/v1/analytics/contributors/top
**Purpose**: Get top contributors by message count
**Query Parameters**:
- `group_id` (UUID, required)
- `start_date`, `end_date` (string, required)
- `limit` (int, optional): Default 10

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "rank": 1,
      "member_id": "uuid",
      "display_name": "John Doe",
      "avatar_url": "https://...",
      "message_count": 1247,
      "reaction_count": 456,
      "activity_score": 95.2,
      "status": "Very Active"
    }
  ]
}
```

#### GET /api/v1/analytics/members/leaderboard
**Purpose**: Get member activity leaderboard with pagination
**Query Parameters**:
- `group_id` (UUID, required)
- `start_date`, `end_date` (string, required)
- `page` (int, optional): Default 1
- `limit` (int, optional): Default 10
- `sort_by` (string, optional): activity_score|message_count|reaction_count

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "rank": 1,
      "member_id": "uuid",
      "display_name": "John Doe",
      "avatar_url": "https://...",
      "message_count": 1247,
      "reaction_count": 456,
      "activity_score": 95.2,
      "status": "Very Active"
    }
  ],
  "pagination": {
    "current_page": 1,
    "total_pages": 5,
    "total_items": 48,
    "items_per_page": 10
  }
}
```

### 4. Content Analytics

#### GET /api/v1/analytics/words/cloud
**Purpose**: Get word frequency data for word cloud
**Query Parameters**:
- `group_id` (UUID, required)
- `start_date`, `end_date` (string, required)
- `limit` (int, optional): Default 100
- `min_frequency` (int, optional): Minimum word frequency

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "word": "project",
      "frequency": 156,
      "relative_size": 0.85
    }
  ]
}
```

#### GET /api/v1/analytics/emojis/frequent
**Purpose**: Get most frequently used emojis
**Query Parameters**: Same as word cloud
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "rank": 1,
      "emoji": "🙏",
      "emoji_name": "Praying Hands",
      "usage_count": 234,
      "percentage": 15.6
    }
  ]
}
```

#### GET /api/v1/analytics/sentiment
**Purpose**: Get sentiment analysis breakdown
**Query Parameters**:
- `group_id` (UUID, required)
- `start_date`, `end_date` (string, required)

**Response**:
```json
{
  "success": true,
  "data": {
    "positive": {
      "count": 812,
      "percentage": 65.0,
      "color": "#10b981"
    },
    "neutral": {
      "count": 312,
      "percentage": 25.0,
      "color": "#6b7280"
    },
    "negative": {
      "count": 125,
      "percentage": 10.0,
      "color": "#ef4444"
    }
  },
  "total_analyzed": 1249
}
```

### 5. Recent Activity

#### GET /api/v1/messages/recent
**Purpose**: Get recent messages for activity feed
**Query Parameters**:
- `group_id` (UUID, required)
- `limit` (int, optional): Default 10

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "member": {
        "display_name": "John Doe",
        "avatar_url": "https://..."
      },
      "content": "Hello everyone!",
      "message_type": "text",
      "timestamp": "2024-01-15T10:30:00Z",
      "reactions": [
        {
          "emoji": "👍",
          "count": 3
        }
      ]
    }
  ]
}
```

### 6. WhatsApp Webhook Integration

#### POST /api/v1/webhook/whatsapp
**Purpose**: Receive WhatsApp messages from WhatsApp Business API
**Headers**:
- `X-Webhook-Token`: Group-specific webhook token for security
- `Content-Type`: application/json

**Request Body**:
```json
{
  "object": "whatsapp_business_account",
  "entry": [
    {
      "id": "group_id",
      "changes": [
        {
          "value": {
            "messaging_product": "whatsapp",
            "metadata": {
              "display_phone_number": "**********",
              "phone_number_id": "phone_id"
            },
            "messages": [
              {
                "from": "sender_phone",
                "id": "message_id",
                "timestamp": "**********",
                "text": {
                  "body": "Hello world!"
                },
                "type": "text"
              }
            ]
          }
        }
      ]
    }
  ]
}
```

**Response**:
```json
{
  "success": true,
  "message": "Webhook processed successfully",
  "processed_messages": 1
}
```

### 7. Group Management

#### GET /api/v1/groups
**Purpose**: Get list of WhatsApp groups
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "group_id": "whatsapp_group_id",
      "name": "Team Chat",
      "description": "Main team communication",
      "member_count": 25,
      "avatar_url": "https://...",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### GET /api/v1/groups/{group_id}/stats
**Purpose**: Get basic group statistics
**Response**:
```json
{
  "success": true,
  "data": {
    "total_members": 25,
    "total_messages": 15420,
    "avg_daily_messages": 42.3,
    "most_active_member": "John Doe",
    "group_age_days": 365,
    "last_activity": "2024-01-15T10:30:00Z"
  }
}
```

## Error Response Format

All endpoints return errors in this format:
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid date format",
    "details": {
      "field": "start_date",
      "expected": "YYYY-MM-DD"
    }
  }
}
```

## Authentication

All endpoints (except webhook) require JWT authentication:
- Header: `Authorization: Bearer <jwt_token>`
- Token contains user ID and role information
- Tokens expire after 24 hours
