# WhatsInsight Backend

> **WhatsApp Group Analytics & Management Platform Backend**  
> Built with Go 1.24, PostgreSQL, and TimescaleDB for high-performance analytics

## 🚀 Quick Start

### Prerequisites
- Go 1.24+
- PostgreSQL 15+ with TimescaleDB extension
- Redis 6+
- Docker & Docker Compose (optional)

### Development Setup

1. **Clone and Setup**
```bash
git clone <repository-url>
cd whatsinsight-backend
cp config/config.example.yaml config/config.yaml
```

2. **Database Setup**
```bash
# Start PostgreSQL with TimescaleDB
docker run -d --name whatsinsight-db \
  -e POSTGRES_DB=whatsinsight \
  -e POSTGRES_USER=whatsinsight \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  timescale/timescaledb:latest-pg15

# Run migrations
psql -h localhost -U whatsinsight -d whatsinsight -f database/schema.sql
```

3. **Redis Setup**
```bash
docker run -d --name whatsinsight-redis \
  -p 6379:6379 \
  redis:7-alpine
```

4. **Run Application**
```bash
go mod download
go run cmd/server/main.go
```

The API will be available at `http://localhost:8080`

### Docker Compose (Recommended)
```bash
docker-compose up -d
```

## 📊 API Documentation

### Core Endpoints

#### Analytics Timeline
```http
GET /api/v1/analytics/timeline/daily?group_id={uuid}&start_date=2024-01-01&end_date=2024-12-31
GET /api/v1/analytics/timeline/weekly?group_id={uuid}&start_date=2024-01-01&end_date=2024-12-31
GET /api/v1/analytics/timeline/monthly?group_id={uuid}&start_date=2024-01-01&end_date=2024-12-31
```

#### Activity Patterns
```http
GET /api/v1/analytics/activity/busy-days?group_id={uuid}&start_date=2024-01-01&end_date=2024-12-31
GET /api/v1/analytics/activity/busy-months?group_id={uuid}&start_date=2024-01-01&end_date=2024-12-31
GET /api/v1/analytics/messages/by-hour?group_id={uuid}&start_date=2024-01-01&end_date=2024-12-31
```

#### Member Analytics
```http
GET /api/v1/analytics/contributors/top?group_id={uuid}&start_date=2024-01-01&end_date=2024-12-31&limit=10
GET /api/v1/analytics/members/leaderboard?group_id={uuid}&start_date=2024-01-01&end_date=2024-12-31&page=1&limit=10
```

#### Content Analytics
```http
GET /api/v1/analytics/words/cloud?group_id={uuid}&start_date=2024-01-01&end_date=2024-12-31&limit=100
GET /api/v1/analytics/emojis/frequent?group_id={uuid}&start_date=2024-01-01&end_date=2024-12-31&limit=20
GET /api/v1/analytics/sentiment?group_id={uuid}&start_date=2024-01-01&end_date=2024-12-31
```

#### WhatsApp Webhook
```http
POST /api/v1/webhook/whatsapp
```

### Authentication
All endpoints (except webhook) require JWT authentication:
```http
Authorization: Bearer <jwt_token>
```

### Swagger Documentation
Available at `http://localhost:8080/swagger/index.html` (development only)

## 🏗️ Architecture

### Clean Architecture Layers
```
Interface Layer (HTTP Handlers, Middleware)
    ↓
Application Layer (Services, Use Cases)
    ↓
Domain Layer (Entities, Interfaces)
    ↓
Infrastructure Layer (Database, Redis, External APIs)
```

### Key Components

#### Database Schema
- **TimescaleDB Hypertables**: Optimized for time-series message data
- **Aggregation Tables**: Pre-computed analytics for performance
- **Indexes**: Optimized for common query patterns

#### Caching Strategy
- **L1 Cache**: In-memory for hot data (5 minutes TTL)
- **L2 Cache**: Redis for warm data (1 hour TTL)
- **L3 Cache**: Redis for cold data (24 hours TTL)

#### Background Processing
- **Message Ingestion**: Real-time processing via Redis Streams
- **Analytics Aggregation**: Scheduled background jobs
- **Cache Warming**: Proactive cache population

## 🔧 Configuration

### Environment Variables
```bash
# Server Configuration
SERVER_PORT=8080
SERVER_ENVIRONMENT=development

# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USER=whatsinsight
DATABASE_PASSWORD=password
DATABASE_DBNAME=whatsinsight

# Redis Configuration
REDIS_ADDRESS=localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-secret-key-change-in-production
JWT_EXPIRY_HOURS=24

# WhatsApp Configuration
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_WEBHOOK_SECRET=your_webhook_secret
```

### Configuration File
See `config/config.yaml` for detailed configuration options.

## 🚀 Performance Features

### Database Optimization
- **TimescaleDB**: Automatic partitioning and compression
- **Continuous Aggregates**: Pre-computed analytics views
- **Optimized Indexes**: Fast queries for time-series data

### API Performance
- **Response Caching**: Multi-level caching strategy
- **Connection Pooling**: Optimized database connections
- **Rate Limiting**: Protection against abuse
- **Compression**: Gzip compression for large responses

### Scalability
- **Stateless Design**: Horizontal scaling ready
- **Background Workers**: Async processing for heavy tasks
- **Load Balancer Ready**: Multiple instances support

## 🔒 Security

### Authentication & Authorization
- **JWT Tokens**: Stateless authentication
- **Role-based Access**: Admin vs Viewer permissions
- **Webhook Validation**: HMAC-SHA256 signature verification

### Data Protection
- **Input Validation**: Comprehensive request validation
- **SQL Injection Prevention**: Parameterized queries
- **Rate Limiting**: Per-user and per-endpoint limits
- **CORS Configuration**: Secure cross-origin requests

## 📈 Monitoring

### Health Checks
```http
GET /health              # Basic health check
GET /health/detailed     # Detailed system status
GET /metrics             # Prometheus metrics
```

### Metrics Collection
- **HTTP Metrics**: Request count, duration, status codes
- **Database Metrics**: Query performance, connection pool status
- **Cache Metrics**: Hit rates, memory usage
- **Business Metrics**: Message processing rates, user activity

### Logging
- **Structured Logging**: JSON format with correlation IDs
- **Request Tracing**: End-to-end request tracking
- **Error Tracking**: Comprehensive error logging

## 🧪 Testing

### Run Tests
```bash
# Unit tests
go test ./...

# Integration tests
go test -tags=integration ./...

# Load tests
go test -tags=load ./...
```

### Test Coverage
```bash
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

## 🚀 Deployment

### Docker Build
```bash
docker build -t whatsinsight-backend .
```

### Kubernetes
```bash
kubectl apply -f k8s/
```

### Environment Setup
1. **Development**: Local setup with Docker Compose
2. **Staging**: Kubernetes cluster with reduced resources
3. **Production**: Kubernetes cluster with full monitoring

## 📚 Development

### Project Structure
```
backend/
├── cmd/server/           # Application entry point
├── internal/
│   ├── config/          # Configuration management
│   ├── domain/          # Domain entities and interfaces
│   ├── handlers/        # HTTP handlers
│   ├── services/        # Business logic
│   ├── repositories/    # Data access layer
│   ├── middleware/      # HTTP middleware
│   └── utils/           # Utility functions
├── database/            # Database schema and migrations
├── docs/                # Documentation
└── config/              # Configuration files
```

### Adding New Features
1. Define domain entities in `internal/domain/`
2. Create repository interfaces and implementations
3. Implement business logic in services
4. Add HTTP handlers with proper validation
5. Write comprehensive tests
6. Update API documentation

### Code Standards
- **Go Conventions**: Follow standard Go conventions
- **Error Handling**: Comprehensive error handling with context
- **Documentation**: Godoc comments for all public functions
- **Testing**: Unit tests for all business logic

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

- **Documentation**: See `/docs` directory
- **API Reference**: Swagger UI at `/swagger/index.html`
- **Issues**: GitHub Issues for bug reports
- **Discussions**: GitHub Discussions for questions

---

**Built with ❤️ for WhatsApp Group Analytics**
